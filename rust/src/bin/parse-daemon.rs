/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--source-log /FOO/BAR/BAZ/logs/2025-06-13--Fri.log \
--log-date 2020-01-02 \
--already-accomplished Sensor1 Sensor2 \
--sensor-list-of-names Sensor1 Sensor2 Sensor3
--sensor-list-of-names-and-addresses *********** Sensor1 *********** Sensor2
--sensor-dict-of-addresses-and-names '{"***********": "Sensor1", "***********": "Sensor2"}'
*/

use clap::Parser;
use std::collections::HashMap;

use eterna::utils_classes::{
    <PERSON><PERSON>onfi<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
};

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/logs/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2"]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3"]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********"]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\"}"
}

fn main() {
    let args = Args::parse();
    println!("Source log: {:?}", args.source_log);
    println!("Log date: {:?}", args.log_date);
    println!("Already accomplished: {:?}", args.already_accomplished);
    println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // create dictionary of instances
    let sensor_names_and_instances: HashMap<String, DaemonParser> = args.sensor_list_of_names
        .iter()
        .map(|s_n| {
            (
                s_n.clone(),
                DaemonParser::new(
                    DaemonConfig::SLUG.value_string(),
                    args.log_date.clone(),
                    s_n.clone(),
                )
            )
        })
        .collect();

    println!("Created {} DaemonParser instances:", sensor_names_and_instances.len());
    for (sensor_name, instance) in &sensor_names_and_instances {
        println!(
            "  {}: slug={}, ymd={}, object_name={}",
            sensor_name,
            instance.slug,
            instance.ymd,
            instance.object_name,
        );
    }
}
