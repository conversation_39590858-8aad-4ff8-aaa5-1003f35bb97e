/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--source-log /FOO/BAR/BAZ/logs/2025-06-13--Fri.log \
--log-date 2020-01-02 \
--already-accomplished Sensor1 Sensor2 \
--sensor-list-of-names Sensor1 Sensor2 Sensor3
--sensor-list-of-names-and-addresses *********** Sensor1 *********** Sensor2
--sensor-dict-of-addresses-and-names '{"***********": "Sensor1", "***********": "Sensor2"}'
*/

use clap::Parser;
use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON>uf<PERSON><PERSON>, <PERSON>uf<PERSON>eader};
use std::time::Instant;
use rayon::prelude::*;
use serde_json;

use eterna::utils_classes::{
    <PERSON><PERSON>onfi<PERSON>,
    Daemon<PERSON><PERSON>er,
    MYSQLConfig,
    ConfigType,
};
use eterna::utils_parsers::parse_ln;
use eterna::utils_constants::ACTION_ON_ERROR;

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/logs/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2"]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3"]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********"]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\"}"
}

// Helper function to convert seconds to human readable format
fn convert_second(seconds: u64, verbose: bool) -> String {
    if seconds == 0 {
        return "0".to_string();
    }

    if seconds < 1 {
        return "~0".to_string();
    }

    if !verbose {
        // Simple format for non-verbose
        let hours = seconds / 3600;
        let minutes = (seconds % 3600) / 60;
        let secs = seconds % 60;

        if hours > 0 {
            format!("{}h {}m {}s", hours, minutes, secs)
        } else if minutes > 0 {
            format!("{}m {}s", minutes, secs)
        } else {
            format!("{}s", secs)
        }
    } else {
        // Verbose format similar to JavaScript version
        let yy = seconds / (3600 * 24 * 30 * 12);
        let mo = (seconds / (3600 * 24 * 30)) % 12;
        let dd = (seconds / (3600 * 24)) % 30;
        let hh = (seconds / 3600) % 24;
        let mi = (seconds / 60) % 60;
        let ss = seconds % 60;

        let mut result = String::new();
        if yy > 0 { result.push_str(&format!("{} year{}, ", yy, if yy > 1 { "s" } else { "" })); }
        if mo > 0 { result.push_str(&format!("{} month{}, ", mo, if mo > 1 { "s" } else { "" })); }
        if dd > 0 { result.push_str(&format!("{} day{}, ", dd, if dd > 1 { "s" } else { "" })); }
        if hh > 0 { result.push_str(&format!("{} hr{}, ", hh, if hh > 1 { "s" } else { "" })); }
        if mi > 0 { result.push_str(&format!("{} min{}, ", mi, if mi > 1 { "s" } else { "" })); }
        if ss > 0 { result.push_str(&format!("{} sec{}", ss, if ss > 1 { "s" } else { "" })); }

        // Remove trailing comma and space
        if result.ends_with(", ") {
            result.truncate(result.len() - 2);
        }

        result
    }
}

fn main() {
    let args = Args::parse();
    println!("Source log: {:?}", args.source_log);
    println!("Log date: {:?}", args.log_date);
    println!("Already accomplished: {:?}", args.already_accomplished);
    println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // Parse the JSON string for sensor dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor dict JSON");

    // create dictionary of instances
    let mut sensor_names_and_instances: HashMap<String, DaemonParser> = args.sensor_list_of_names
        .iter()
        .map(|s_n| {
            (
                s_n.clone(),
                DaemonParser::new(
                    DaemonConfig::SLUG.value_string(),
                    args.log_date.clone(),
                    s_n.clone(),
                )
            )
        })
        .collect();

    println!("Created {} DaemonParser instances:", sensor_names_and_instances.len());
    for (sensor_name, instance) in &sensor_names_and_instances {
        println!(
            "  {}: slug={}, ymd={}, object_name={}",
            sensor_name,
            instance.slug,
            instance.ymd,
            instance.object_name,
        );
    }

    // Convert lines 253-278 from Python: parallel parsing with timing
    match File::open(&args.source_log) {
        Ok(file) => {
            let reader = BufReader::new(file);
            let lines: Vec<String> = reader.lines()
                .filter_map(|line| line.ok())
                .collect();

            println!("parsing...");
            let parse_start = Instant::now();

            // Use rayon for parallel processing instead of Python's Pool
            let chunk_size = match MYSQLConfig::POOL_CHUNKSIZE.value() {
                eterna::utils_classes::MYSQLValue::Int(size) => size as usize,
                _ => 100_000, // fallback
            };

            // Process lines in parallel chunks
            let valid_lines: Vec<(Option<String>, Option<Vec<String>>)> = lines
                .par_chunks(chunk_size)
                .flat_map(|chunk| {
                    chunk.par_iter().map(|line| {
                        parse_ln(
                            line.trim(),
                            ConfigType::DaemonConfig,
                            &args.sensor_list_of_names_and_addresses,
                            &sensor_dict_of_addresses_and_names,
                        )
                    })
                })
                .collect();

            // Process results and populate sensor instances
            for (sensor_name_opt, parsed_ln_opt) in valid_lines {
                if let (Some(sensor_name), Some(parsed_ln)) = (sensor_name_opt, parsed_ln_opt) {
                    // Skip if sensor already accomplished
                    if args.already_accomplished.contains(&sensor_name) {
                        continue;
                    }

                    // Add parsed line to the appropriate sensor instance
                    if let Some(instance) = sensor_names_and_instances.get_mut(&sensor_name) {
                        instance.rows.push(parsed_ln);
                    }
                }
            }

            let parse_end = Instant::now();
            let parse_duration = parse_end.duration_since(parse_start).as_secs();
            println!(
                "parsed in {:,} seconds ({})",
                parse_duration,
                convert_second(parse_duration, false)
            );

            // Print summary of parsed data
            for (sensor_name, instance) in &sensor_names_and_instances {
                if !instance.rows.is_empty() {
                    println!("  {}: {} rows parsed", sensor_name, instance.rows.len());
                }
            }
        }
        Err(e) => {
            eprintln!("Error opening source log file '{}': {}", args.source_log, e);
            std::process::exit(1);
        }
    }
}
